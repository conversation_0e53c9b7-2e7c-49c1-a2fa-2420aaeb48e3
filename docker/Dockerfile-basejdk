FROM openjdk:8-alpine
MAINTAINER feiyuming
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories \
	&& apk update \
	&& apk add curl \
	&& apk add busybox-extras \
    && apk add tini \
    && apk --no-cache add curl \
    && apk add --update ttf-dejavu fontconfig \
    && apk --no-cache add libc6-compat && ln -s /lib/libc.musl-x86_64.so.1 /lib/ld-linux-x86-64.so.2 \
    && rm -f /etc/localtime \
    && ln -sv /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone
VOLUME /tmp
WORKDIR /home/<USER>
COPY --from=hengyunabc/arthas:latest /opt/arthas opt/arthas
ENTRYPOINT ["/sbin/tini", "--"]