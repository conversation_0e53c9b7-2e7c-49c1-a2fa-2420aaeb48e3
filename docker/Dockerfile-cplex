FROM harbor.fuyaogroup.com/bpim/ubuntu:2.0.0
MAINTAINER feiyuming
RUN pip3 install -i https://mirrors.aliyun.com/pypi/simple/ setuptools==68.0.0 pip==20.0.2 wheel==0.34.2 \
    && pip3 install -i https://mirrors.aliyun.com/pypi/simple/ certifi==2019.9.11 chardet==3.0.4 idna==2.8 \
    && pip3 install -i https://mirrors.aliyun.com/pypi/simple/ urllib3==1.25.11 requests==2.22.0 six==1.12.0 \
    && pip3 install -i https://mirrors.aliyun.com/pypi/simple/ ply==3.11 python-dateutil==2.9.0.post0 pytz==2024.2 \
    && pip3 install -i https://mirrors.aliyun.com/pypi/simple/ tzdata==2024.2 nose==1.3.7 PyUtilib==6.0.0 \
    && pip3 install -i https://mirrors.aliyun.com/pypi/simple/ docloud==1.0.375 docplex==2.25.236 cplex==******** \
    && pip3 install -i https://mirrors.aliyun.com/pypi/simple/ gurobipy==9.1.0 numpy==1.24.4 pandas==2.0.3 \
    && pip3 install -i https://mirrors.aliyun.com/pypi/simple/ Pyomo==5.7.3 matplotlib==3.7.5 scikit_learn==1.3.2 \
    && pip3 install -i https://mirrors.aliyun.com/pypi/simple/ statsmodels==0.14.1 xgboost==2.1.1