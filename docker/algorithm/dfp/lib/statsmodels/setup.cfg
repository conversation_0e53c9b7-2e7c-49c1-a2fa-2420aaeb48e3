[metadata]
license_files =
    LICENSE.txt

[tool:pytest]
minversion = 6
testpaths = statsmodels
xfail_strict = true
addopts = --strict
# Filter warnings generated by dependencies
filterwarnings =
    ignore:pandas.core.common.is_categorical_dtype:DeprecationWarning:patsy
    ignore:Using or importing the ABCs:DeprecationWarning:patsy
    ignore:Using a non-tuple sequence:FutureWarning:mkl_fft
    ignore:Using a non-tuple:FutureWarning:scipy.signal
    ignore:Using a non-tuple:FutureWarning:scipy.stats.stats
    ignore:the matrix subclass is not the recommended:PendingDeprecationWarning:
    ignore:Method .ptp is deprecated:FutureWarning:numpy.core.fromnumeric
    ignore::DeprecationWarning:nbconvert.exporters.exporter_locator
    ignore:Using or importing the ABCs:DeprecationWarning:jinja2.utils
    ignore:Converting `np.integer`:DeprecationWarning:scipy.stats.morestats
    ignore:\nstatsmodels.tsa.arima_model.ARMA:FutureWarning
    ignore:The --strict option is deprecated:pytest.PytestDeprecationWarning:
    error:genfromdta:FutureWarning
    error:StataReader:FutureWarning
    error:Care should be used:UserWarning
    error::statsmodels.tools.sm_exceptions.HypothesisTestWarning
    error::statsmodels.tools.sm_exceptions.SpecificationWarning
    error:load will return datasets:FutureWarning
    error:the 'lags' keyword is deprecated:FutureWarning
    error:nobs is deprecated in favor of lags:DeprecationWarning
    error:The default pvalmethod will change:FutureWarning
    error:Using an implicitly registered:FutureWarning
    error:An unsupported index:statsmodels.tools.sm_exceptions.ValueWarning
    error:No supported index is:statsmodels.tools.sm_exceptions.ValueWarning
    error:Anscombe residuals:FutureWarning
    error:Calling Family:DeprecationWarning
    error:SIR.fit_regularized did not:UserWarning
    error:the 'sigma' keyword:FutureWarning
    error:tight_layout:UserWarning
    error:statsmodels.tsa.AR has been deprecated:FutureWarning
    error:Using deprecated variance components:UserWarning
    error:recarray support has been deprecated:FutureWarning
    error:The value returned will change to a:FutureWarning
    error:The default value of lags:FutureWarning
    error:non-integer arg n is deprecated:DeprecationWarning
    error:Creating an ndarray:numpy.VisibleDeprecationWarning
    error:The default number of lags:FutureWarning:
    error:fft=True will become the default:FutureWarning
    error:The parameter names will change:FutureWarning
    error:Only PeriodIndexes, DatetimeIndexes:UserWarning
    error:the 'unbiased'' keyword is deprecated:FutureWarning
    error:unbiased is deprecated in factor of adjusted:FutureWarning
    error:categorical is deprecated:FutureWarning
    error:After 0.13 initialization:FutureWarning
    error:The bic value:FutureWarning
    error:Setting initial values:FutureWarning
    error:Setting use_boxcox:FutureWarning
    error:``Describe`` has been deprecated:DeprecationWarning
    error:Promotion of numbers and bools to strings:FutureWarning
    error:old_names will be removed after the 0.14 release:FutureWarning
    error:Dropping of nuisance columns in::
    error:The `inplace` parameter in pandas::
    error:The default method:FutureWarning:
    error:trend 'nc' has been renamed to 'n':FutureWarning:
    error:Keyword arguments have been passed:FutureWarning:
    error:The behavior of wald_test:FutureWarning
    error:cols is deprecated and will be removed after:FutureWarning
    error:Passing None to catch any warning has been deprecated::
    error:pandas.Int64Index is deprecated::
    error:divide by zero encountered in _binom_pdf:RuntimeWarning
    error:verbose is deprecated:FutureWarning
    error:linear keyword is deprecated:DeprecationWarning:
    error:The identity link alias is deprecated:FutureWarning:
    error:The log link alias is deprecated:FutureWarning:
    error:The probit link alias is deprecated:FutureWarning:
    error:Parsing dates in:UserWarning
    error:A value is trying to be set on a copy::
    error:Conversion of an array with ndim:DeprecationWarning:
    error:Series.__getitem__ treating keys:FutureWarning:
    error:'Y' is deprecated, please use 'YE' instead:FutureWarning
    error:'A' is deprecated, please use 'YE' instead:FutureWarning
    error:'H' is deprecated and will:FutureWarning
    error:'M' is deprecated, please use:FutureWarning
    error:'Q' is deprecated, please use 'QE' instead:FutureWarning
    error:'Q-DEC' is deprecated, please use 'QE-DEC' instead:FutureWarning
    error:'Q-JAN' is deprecated, please use 'QE-JAN' instead:FutureWarning
    error:'BQ-MAR' is deprecated, please use 'BQE-MAR' instead:FutureWarning
    error:'AS-MAR' is deprecated and will be removed in a future version:FutureWarning
    error:Downcasting object dtype arrays on:FutureWarning
    error:The previous implementation of stack is deprecated:FutureWarning
    error:Series.__setitem__ treating keys as positions is deprecated:FutureWarning
    error:The provided callable:FutureWarning
    error:divide by zero encountered in log1p:RuntimeWarning

markers =
    example: mark a test that runs example code
    matplotlib: mark a test that requires matplotlib
    slow: mark a test as slow
    smoke: mark a test as a smoketest
    low_precision: mark a test as low precision
    todo: mark a test as incomplete
junit_family = xunit2

[flake8]
exclude=.git,build,docs,archive
ignore=
    W503,
    # W503: line break before binary operator
    W504,
    # W504: line break after binary operator
    E741,
    # E741: ambiguous variable name 'l'
    E203
    # E203: Whitespace before ':'
select=
    E101,
    # E101: indentation contains mixed spaces and tabs
    E124,
    # E124: closing bracket does not match visual indentation
    F811,
    # F811: redefinition of unused 'pytest' from line 10
    F812,
    # F812: list comprehension redefines 'x' from line 199
    F822,
    # F822: undefined name name in __all__
    F823,
    # F823: local variable name ... referenced before assignment
    E129,
    # E129: visually indented line with same indent as next logical line
    E131,
    # E131: continuation line unaligned for hanging indent
    E125,
    # E125: continuation line with same indent as next logical line
    E111,
    # E111: Indentation is not a multiple of four
    E114,
    # E114: Indentation is not a multiple of four (comment)
    E117,
    # E117: over-indented
    E227,
    # E227: missing whitespace around bitwise or shift operator
    E228,
    # E228: missing whitespace around modulo operator
    E211,
    # E211: whitespace before '['
    E112,
    # E112: expected an indented block
    E113,
    # E113: unexpected indentation
    E223,
    # E223: tab before operator
    E224,
    # E224: tab after operator
    E242,
    # E242: tab after ','
    E304,
    # E304: blank lines found after function decorator
    F831,
    # F831: duplicate argument name in function definition
    E306,
    # E306: expected 1 blank line before a nested definition, found 0

    W,
    # W191: indentation contains tabs
    # W291: trailing whitespace
    # W292: no newline at end of file
    # W293: blank line contains whitespace
    # W391: blank line at end of file
    # W601: .has_key() is deprecated, use 'in'
    # W602: deprecated form of raising exception
    # W603: '<>' is deprecated, use '!='
    # W604: backticks are deprecated, use 'repr()'
    # W605: invalid escape sequence 'x'
    # W606: 'async' and 'await' are reserved keywords starting with Python 3.7

    E70,
    # E701: multiple statements on one line (colon)
    # E702: multiple statements on one line (semicolon)
    # E703: statement ends with a semicolon
    # E704: multiple statements on one line (def)

    E71,
    # E711: comparison to None should be 'if cond is None:'
    # E712: comparison to True should be 'if cond is True:' or 'if cond:'
    # E713: test for membership should be 'not in'

    E721,
    # E721: do not compare types, use 'isinstance()'

    E74,
    # E741 ambiguous variable name 'l', 'O', or 'I'
    # E742: do not define classes named 'l', 'O', or 'I'
    # E743: do not define functions named 'l', 'O', or 'I'

    F4,
    # F401: 'foo.bar' imported but unused
    # F402: import 'assert_equal' from line 7 shadowed by loop variable
    # F403: 'from .data import *' used; unable to detect undefined names
    # F404: future import(s) name after other statements
    # F405: name may be undefined, or defined from star imports: module
    # F406: 'from module import *' only allowed at module level
    # F407: an undefined __future__ feature name was imported

    F6,
    # F601: dictionary key name repeated with different values
    # F602: dictionary key variable name repeated with different values
    # F621: too many expressions in an assignment with star-unpacking
    # F622: two or more starred expressions in an assignment (a, *b, *c = d)
    # F631: assertion test is a tuple, which are always True
    # F632: use ==/!= to compare str, bytes, and int literals

    F7,
    # F701: a break statement outside of a while or for loop
    # F702: a continue statement outside of a while or for loop
    # F703: a continue statement in a finally block in a loop
    # F704: a yield or yield from statement outside of a function
    # F705: a return statement with arguments inside a generator
    # F706: a return statement outside of a function/method
    # F707: an except: block as not the last exception handler
    # F721: doctest syntax error
    # F722: syntax error in forward type annotation

    F81,
    # F811: redefinition of unused 'pytest' from line 10
    # F812: list comprehension redefines 'x' from line 199

    F82,
    # F821: undefined name 'foo'
    # F822: undefined name name in __all__
    # F823: local variable name ... referenced before assignment

    F9,
    # F901: raise NotImplemented should be raise NotImplementedError

    E27,
    # E271: multiple spaces after keyword
    # E272: multiple spaces before keyword
    # E273: tab after keyword
    # E274: tab before keyword
    # E275: missing whitespace after keyword

    E4,
    # E401: multiple imports on one line
    # E402: module level import not at top of file

    E9,
    # E901: SyntaxError or IndentationError
    # E902: IOError
    # E999: Syntax Error


[coverage:run]
source = statsmodels
branch = True
plugins = Cython.Coverage
omit =
    # print_version is untestable
    */print_version.py
    # skip compatibility code
    */compat/*
    # Unused file
    */results/gee_generate_tests.py
    # Results for tests
    */tests/results/*
    # Simulation checks
    */tests/*_simulation_check.py
    # Misc non-test files in test directories
    */tests/_*
    */tests/e*
    */tests/c*
    */tests/d*
    */tests/g*
    */tests/m*
    */tests/p*
    */tests/r*
    */tests/s*
    */_version.py

[coverage:report]
show_missing = True
ignore_errors = False
# Regexes for lines to exclude from consideration
exclude_lines =
    # Have to re-enable the standard pragma
    pragma: no cover

    # Do not complain about missing debug-only code:
    def __repr__
    if self\.debug
    if pdf_output:

    # Do not complain if tests do not hit defensive assertion code:
    raise AssertionError
    raise NotImplementedError
    except NotImplementedError
    except ImportError
    except (HTTPError, URLError, SSLError, timeout)
    except IGNORED_EXCEPTIONS

    # Ignore pass
    pass

    # Do not complain if non-runnable code is not run:
    if 0:
    if __name__ == .__main__.:

[coverage:html]
directory = coverage_html_report

[isort]
sections=FUTURE,COMPAT,STDLIB,THIRDPARTY,FIRSTPARTY,LOCALFOLDER
known_first_party=statsmodels
known_third_party=Cython,numpy,matplotlib,pandas,patsy,pytest,cvxopt,cython
multi_line_output=3
include_trailing_comma=True
use_parentheses=True
line_length=79
known_compat=statsmodels.compat.*,setuptools,setuptools.*
force_grid_wrap=0
combine_as_imports=True
force_sort_within_sections=True
force_to_top=True
